# 1. Statistical Validation

Thank you very much for your thoughtful and constructive feedback. Following your suggestions, we employed statistical tests (using bootstrap by default) to evaluate our claims. In the process, we uncovered several interesting new findings. Due to space limitation, we can only present the test results and basic conclusions here; the complete revised claims and new findings will be included in the revised manuscript.


Default: CI denotes the confidence interval at α = 0.1, and p is the one‑ (to test whether one is significantly greater than another) or two‑sided (to test whether there is a significant difference between two) p‑value.

## Chapter 5
### Impact of Continuous Pre‑Training
**Claim (line 274):** “CPT significantly improves model performance on ANESBENCH, ... across all SFT data configurations.”

| $H_1$ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA (w CPT) > AnesQA (w/oCPT) | 0.0036 | 0.3478 | (-0.0111, 0.0178) |
| Medical-o1 (w CPT) > AnesQA (w/oCPT) | 0.0151 | 0.0483 | (0.0002, 0.0298) |
| AnesQA + Medical-o1 (w CPT) > AnesQA + Medical-o1 (w/oCPT) | 0.0149 | 0.0413 | (0.0009, 0.0287) |

- With AnesQA‑only SFT, CPT does not yield a significant gain.
- With Medical‑o1 and AnesQA+Medical‑o1 SFT, CPT yields significant improvements.

**Claim (line 278):** “Using the full SFT dataset ... CPT model, bringing it close to Qwen2.5‑7B‑Instruct.”

| $H_1$ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA + Medical-o1 (w CPT) ≠ Qwen2.5‑7B‑Instruct | -0.0029 | 0.7445 | (-0.017, 0.0113) |

- No significant difference between AnesQA + Medical‑o1 (w CPT) and Qwen2.5‑7B‑Instruct; describing them as “close” is reasonable.

### Complementarity of AnesQA and Medical‑o1
**Claim (line 283):** “Neither dataset alone is sufficient to optimize performance across all benchmarks; ... yields the best results.”

**Overall**

| $H_1$ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA+Medical(w/o CPT) > AnesQA(w/o CPT) | 0.0041 | 0.2794 | (-0.0079, 0.0149) |
| AnesQA+Medical(w/o CPT) > Medical(w/o CPT) | 0.0059 | 0.2216 | (-0.007, 0.0185) |
| AnesQA+Medical(w CPT) > AnesQA(w CPT) | 0.0154 | 0.0237 | (0.0029, 0.0278) |
| AnesQA+Medical(w CPT) > Medical(w CPT) | 0.0056 | 0.2284 | (-0.0068, 0.0181) |

- Only the comparison between AnesQA+Medical (w/ CPT) and AnesQA (w/ CPT) shows statistical significance. Therefore, we narrow the scope to the system level and obtain the following results.

**Per‑System (without AnesCorpus CPT)**

| $H_1$ | Delta | p | CI|
|:--|--:|--:|:--|
| AnesQA+Medical(System1) > AnesQA(System1) | 0.0003 | 0.4948 | (-0.0126, 0.0133) |
| AnesQA+Medical(System1) > Medical-o1(System1) | 0.012 | 0.0936 | (-0.0027, 0.0266) |
| AnesQA+Medical(System1.x) > AnesQA(System1.x) | -0.0193 | 0.7863 | (-0.0635, 0.0249) |
| AnesQA+Medical(System1.x) > Medical-o1(System1.x) | -0.0249 | 0.8419 | (-0.0691, 0.0193) |
| AnesQA+Medical(System2) > AnesQA(System2) | 0.0226 | 0.0646 | (-0.0009, 0.0472) |
| AnesQA+Medical(System2) > Medical-o1(System2) | -0.0009 | 0.5219 | (-0.0283, 0.0264) |
- Adding AnesQA  tends to enhance system1 ability.
- Adding Medical‑o1 tends to enhance system2 ability.
- Consistent with: no single dataset suffices to optimize across all levels.

**Per‑System (with AnesCorpus CPT)**

| $H_1$ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA+Medical(System1) > AnesQA(System1) | 0.0383 | 0.0001 | (0.0233, 0.0529) |
| AnesQA+Medical(System1) > Medical-o1(System1) | 0.0273 | 0.0008 | (0.0123, 0.0419) |
| AnesQA+Medical(System1.x) > AnesQA(System1.x) | -0.0083 | 0.6286 | (-0.0552, 0.0387) |
| AnesQA+Medical(System1.x) > Medical-o1(System1.x) | -0.011 | 0.6608 | (-0.0552, 0.0359) |
| AnesQA+Medical(System2) > AnesQA(System2) | -0.0415 | 0.9964 | (-0.0679, -0.0151) |
| AnesQA+Medical(System2) > Medical-o1(System2) | -0.05 | 0.9993 | (-0.0764, -0.0236) |
- After CPT, adding extra SFT data only boosts System1.
- This aligns with reports that single‑domain CPT could induce catastrophic forgetting, weakening higher‑level reasoning (Reference 1).

### Impact of Reasoning Techniques
**Claim (line 295):** “Beam Search consistently outperforms Best‑of‑N, indicating step‑level structured exploration benefits reasoning.”

| $H_1$ | Delta | p | CI |
|:--|--:|--:|:--|
| BeamSearch(TW4) > Best-of-N(TW4) | 0.0163 | 0.0156 | (0.0038, 0.0287) |
| BeamSearch(TW8) > Best-of-N(TW8) | 0.009 | 0.1077 | (-0.0027, 0.0206) |
- Only when the tree width equals 4 does Beam Search significantly outperform Best‑of‑N. There is insufficient evidence that Beam Search consistently and significantly outperforms Best‑of‑N.

**Claim (line 296):** “Increasing beam size yields incremental accuracy improvements.”

| $H_1$ | Delta | p | CI |
|:--|--:|--:|:--|
| BeamSearch(BS8,Overall) > BeamSearch(BS4,Overall) | 0.0036 | 0.3153 | (-0.0081, 0.0154) |
| BeamSearch(BS8,System1) > BeamSearch(BS4,System1) | 0 | 0.509 | (-0.017, 0.017) |
| BeamSearch(BS8,System1.x) > BeamSearch(BS4,System1.x) | -0.002 | 0.562 | (-0.030, 0.026) |
| BeamSearch(BS8,System2) > BeamSearch(BS4,System2) | 0.05 | 0.041 | (-0.006, 0.105) |
- Overall, Beam Size 8 vs. Beam Size 4 shows no significant improvement.
- Only System2 exhibits a significant gain with a larger beam, suggesting that step‑level structured expoloration in Beam Search benefits reasoning more;
### Evaluation of Distillation Performance
**Claim (line 309):** “Models initialized from Qwen2.5‑32B Base and Llama‑3.3‑70B‑Instruct ... outperformed their instruction‑tuned counterparts in both System 2 and System 1.x type questions.”

DeepSeek‑distilled model > corresponding Instruct model (bootstrap): 
- Qwen2.5‑32B: p=0.00299; 
- Llama‑3.3‑70B: p=0.00009.

Consistent with our claim.

**Claim (line 309):** “However, the effectiveness of R1 distillation correlates positively with model size.”
Spearman correlation between gains from DeepSeek distillation and model size: 
- System1: rho=0.799; p=0.200. 
- System1.x: rho=1.0; p=0. 
- System2: rho=1.0; p=0.

Consistent with our claim for system1.x and system2.

## Chapter 4
### Performance Across Model Scale
**Claim (line 218):** “... reveals a strong positive correlation between model performance and model scale.”
Spearman results (AnesBench/AMCQA × System):
- AnesBench (System1): rho=0.859; p=1.449e‑15; (System1.x): rho=0.861; p=1.079e‑15; (System2): rho=0.868; p=3.312e‑16.
- AMCQA (System1): rho=0.755; p=2.424e‑10; (System1.x): rho=0.801; p=2.991e‑12; (System2): rho=0.794; p=5.839e‑12.

Consistent with our claim.

**Claim (line 218):** “Each unit increase in model scale brings progressively smaller performance gains.”
Quadratic OLS, test H1: β2<0 (AnesBench/AMCQA × System):
- AnesBench: β2=−2.79; p=0.006 (System1); β2=−2.607; p=0.007 (System1.x); β2=−1.920; p=0.008 (System2).
- AMCQA: β2=−2.923; p=0.010 (System1); β2=−2.729; p=0.017 (System1.x); β2=−2.587; p=0.023 (System2).

Consistent with our claim.

**Claim (line 218):** “This indicates that performance gains from increasing model size are notably lower for System2 compared to System1.”
Linear model: y=β0+β1x+β2·group+β3(x×group)+ε, where group indicates x from System1 (or System1.x). Bootstrap one‑sided p for H1:β3>0:
- 0.1538 (System1>System2); 0.0009 (System1.x>System2).

This claim holds only for System 1.x.

### Performance Across Language
**Claim (line 232):** “In contrast, Llama‑3.1‑8B‑based models, ...., perform notably worse in Chinese.”
Bootstrap comparisons (English>Chinese):
- Llama‑3.1‑8B‑Instruct: AnesBench p=9e‑5; AMCQA p=9e‑5.
- UltraMedical‑8B: AnesBench p=9e‑5; AMCQA p=9e‑5.
- Huatuo‑GPT‑8B: AnesBench p=9e‑5; AMCQA p=0.0059.
- FineMedLM: AnesBench p=9e‑5; AMCQA p=9e‑5.
- FineMedLM‑o1: AnesBench p=9e‑5; AMCQA p=9e‑5.

Consistent with our claim.

### Performance Across Output Length
**Claim (line 248):** “Models with longer CoT reasoning processes tend to exhibit superior response performance. ... for system2,...  models achieve higher scores with longer outputs.”
Spearman within each system (output log length vs. score):
- System1: rho=0.2879; p=0.3182.
- System1.x: rho=0.3319; p=0.2464.
- System2: rho=0.4917; p=0.0741.

Consistent with our claim, though with weak evidence(0.05<p<0.1).


# 2. Data quality

Thank you for carefully reviewing our benchmark and datasets.

## AnesQA

Regarding the use of LLMs, we mentioned in line 159 of the main text that AnesQA is generated and filtered using LLMs. We apologize for the oversight in omitting this case in Checklist Item 16.

The incorrect QA examples were caused by the LLM mistakenly outputting the prompt under a one-shot setting. However, such errors are rare (~0.5%) and have minimal impact on the overall dataset quality.

## AnesBench

As noted in Appendix J, our initial quality check involved random sampling of 500 examples, during which no such issues were detected. We found that the issue you mentioned stems from a single data source error.  After thoroughly reviewing **all** data from AnesBench, we identified erroneous question IDs as follows(9/4427 $\approx$ 0.2%; Also rare, minimal impact on the overall benchmark quality):

Error：
33c58654-0add-5ed7-a1f6-4d788b419036
33f6f51b-d715-5c65-945d-34f6aa4362c0
6f32034f-02d5-58d1-84c5-8cf0533fb412
3b6200f0-1747-5b82-92cd-67ea1148357e
5aebf7c7-a549-59a1-b3ae-e3f3e8cdd886

Missing required information:
ee133db3-c6e2-53b1-b601-846c1680ba63
d50c6e15-a4d5-5812-94ea-5d81ce8dee71
4a4a0859-1e82-5614-b0e8-97b9a8c50beb
98f7b24d-252d-5f6e-9b12-e6289489a62b

We will upload the corrected version to HuggingFace and publish an errata list on both GitHub and the project website to ensure ongoing maintenance of our benchmark and dataset.

# 3. Limitation

Thanks for your comment. We believe it is appropriate to include the limitations in the main text. In the revised manuscript, we will report the limitations—such as the limited variety of question types and the noise introduced by the LLM—within the main text for readers’ reference.

# Reference
1. EvoLM: In Search of Lost Language Model Training Dynamics


