# Rebuttal to Reviewer 2 (Score 5; Con 5)

We sincerely thank Reviewer 2 for the thoughtful and constructive feedback. We address each concern below with detailed responses and clarifications.

---

## Response to Statistical Validation

Thank you very much for your thoughtful and constructive feedback. Following your suggestions, we employed statistical tests (using bootstrap by default) to evaluate our claims. In the process, we uncovered several interesting new findings. Due to space limitation, we can only present the test results and basic conclusions here; the complete revised claims and new findings will be included in the revised manuscript.

**Statistical Protocol**: CI = confidence interval (α=0.1); p = one/two-sided p-value

**Abbreviations**: Sys1 = System1, Sys1.x = System1.x, Sys2 = System2, A-QA = AnesQA, M-o1 = Medical-o1

### 5.1 Continuous Pre-Training (CPT) Impact

**Claim 5.1a (L274)**: "CPT significantly improves model performance on AnesBench..."

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA (w/ CPT) > AnesQA (w/o CPT) | 0.0036 | 0.3478 | (-0.0111, 0.0178) |
| Medical-o1 (w/ CPT) > AnesQA (w/o CPT) | 0.0151 | 0.0483 | (0.0002, 0.0298) |
| AnesQA + Medical-o1 (w/ CPT) > AnesQA + Medical-o1 (w/o CPT) | 0.0149 | 0.0413 | (0.0009, 0.0287) |

**Key Findings:**
- With AnesQA-only SFT, CPT does not yield a significant gain.
- With Medical-o1 and AnesQA+Medical-o1 SFT, CPT yields significant improvements.

**Claim 5.1b (L278)**: "Using the full SFT dataset... CPT model, bringing it close to Qwen2.5-7B-Instruct."

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA + Medical-o1 (w/ CPT) ≠ Qwen2.5-7B-Instruct | -0.0029 | 0.7445 | (-0.017, 0.0113) |

**Key Findings:**
- No significant difference between AnesQA + Medical-o1 (w/ CPT) and Qwen2.5-7B-Instruct; describing them as "close" is reasonable.

### 5.2 Dataset Complementarity

**Claim 5.2a (L283)**: "Neither dataset alone is sufficient to optimize performance across all benchmarks..."

**Overall Performance Analysis:**

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA+Medical(w/o CPT) > AnesQA(w/o CPT) | 0.0041 | 0.2794 | (-0.0079, 0.0149) |
| AnesQA+Medical(w/o CPT) > Medical(w/o CPT) | 0.0059 | 0.2216 | (-0.007, 0.0185) |
| AnesQA+Medical(w/ CPT) > AnesQA(w/ CPT) | 0.0154 | 0.0237 | (0.0029, 0.0278) |
| AnesQA+Medical(w/ CPT) > Medical(w/ CPT) | 0.0056 | 0.2284 | (-0.0068, 0.0181) |

**Key Findings:**
- Only the comparison between AnesQA+Medical (w/ CPT) and AnesQA (w/ CPT) shows statistical significance. Therefore, we narrow the scope to the system level and obtain the following results.

**Per-System Analysis (without AnesCorpus CPT):**

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA+Medical(System1) > AnesQA(System1) | 0.0003 | 0.4948 | (-0.0126, 0.0133) |
| AnesQA+Medical(System1) > Medical-o1(System1) | 0.012 | 0.0936 | (-0.0027, 0.0266) |
| AnesQA+Medical(System1.x) > AnesQA(System1.x) | -0.0193 | 0.7863 | (-0.0635, 0.0249) |
| AnesQA+Medical(System1.x) > Medical-o1(System1.x) | -0.0249 | 0.8419 | (-0.0691, 0.0193) |
| AnesQA+Medical(System2) > AnesQA(System2) | 0.0226 | 0.0646 | (-0.0009, 0.0472) |
| AnesQA+Medical(System2) > Medical-o1(System2) | -0.0009 | 0.5219 | (-0.0283, 0.0264) |

**Key Findings:**
- Adding AnesQA tends to enhance system1 ability.
- Adding Medical-o1 tends to enhance system2 ability.
- Consistent with: no single dataset suffices to optimize across all levels.

**Per-System Analysis (with AnesCorpus CPT):**

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| AnesQA+Medical(System1) > AnesQA(System1) | 0.0383 | 0.0001 | (0.0233, 0.0529) |
| AnesQA+Medical(System1) > Medical-o1(System1) | 0.0273 | 0.0008 | (0.0123, 0.0419) |
| AnesQA+Medical(System1.x) > AnesQA(System1.x) | -0.0083 | 0.6286 | (-0.0552, 0.0387) |
| AnesQA+Medical(System1.x) > Medical-o1(System1.x) | -0.011 | 0.6608 | (-0.0552, 0.0359) |
| AnesQA+Medical(System2) > AnesQA(System2) | -0.0415 | 0.9964 | (-0.0679, -0.0151) |
| AnesQA+Medical(System2) > Medical-o1(System2) | -0.05 | 0.9993 | (-0.0764, -0.0236) |

**Key Findings:**
- After CPT, adding extra SFT data only boosts System1.
- This aligns with reports that single-domain CPT could induce catastrophic forgetting, weakening higher-level reasoning (Reference 1).

### 5.3 Reasoning Techniques Impact

**Abbreviations**: BeamSearch = BeaS, Best-of-N = BON, tree width = TW, beam size = BS

**Claim 5.3a (L295)**: "Beam Search consistently outperforms Best-of-N..."

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| BeamSearch(TW4) > Best-of-N(TW4) | 0.0163 | 0.0156 | (0.0038, 0.0287) |
| BeamSearch(TW8) > Best-of-N(TW8) | 0.009 | 0.1077 | (-0.0027, 0.0206) |

**Key Findings:**
- Only when the tree width equals 4 does Beam Search significantly outperform Best-of-N. There is insufficient evidence that Beam Search consistently and significantly outperforms Best-of-N.

**Claim 5.3b (L296)**: "Increasing beam size yields incremental accuracy improvements."

| H₁ | Delta | p | CI |
|:--|--:|--:|:--|
| BeamSearch(BS8,Overall) > BeamSearch(BS4,Overall) | 0.0036 | 0.3153 | (-0.0081, 0.0154) |
| BeamSearch(BS8,System1) > BeamSearch(BS4,System1) | 0 | 0.509 | (-0.017, 0.017) |
| BeamSearch(BS8,System1.x) > BeamSearch(BS4,System1.x) | -0.002 | 0.562 | (-0.030, 0.026) |
| BeamSearch(BS8,System2) > BeamSearch(BS4,System2) | 0.05 | 0.041 | (-0.006, 0.105) |

**Key Findings:**
- Overall, Beam Size 8 vs. Beam Size 4 shows no significant improvement.
- Only System2 exhibits a significant gain with a larger beam, suggesting that step-level structured exploration in Beam Search benefits reasoning more.

### 5.4 Distillation Performance

**Claim 5.4a (L309)**: "Models initialized from Qwen2.5-32B Base and Llama-3.3-70B-Instruct... outperformed their instruction-tuned counterparts..."

**Statistical Results:**
DeepSeek-distilled model > corresponding Instruct model (bootstrap):
- Qwen2.5-32B: p=0.00299
- Llama-3.3-70B: p=0.00009

**Key Findings:**
- Consistent with our claim.

**Claim 5.4b (L309)**: "However, the effectiveness of R1 distillation correlates positively with model size."

**Statistical Results:**
Spearman correlation between gains from DeepSeek distillation and model size:
- System1: rho=0.799; p=0.200
- System1.x: rho=1.0; p=0
- System2: rho=1.0; p=0

**Key Findings:**
- Consistent with our claim for system1.x and system2.

---

## Response to 4.1 Analysis

### 4.1 Performance Across Model Scale

**Claim 4.1a (L218)**: "...reveals a strong positive correlation between model performance and model scale."

**Statistical Results:**
Spearman results (AnesBench/AMCQA × System):
- AnesBench (System1): rho=0.859; p=1.449e-15; (System1.x): rho=0.861; p=1.079e-15; (System2): rho=0.868; p=3.312e-16
- AMCQA (System1): rho=0.755; p=2.424e-10; (System1.x): rho=0.801; p=2.991e-12; (System2): rho=0.794; p=5.839e-12

**Key Findings:**
- Consistent with our claim.

**Claim 4.1b (L218)**: "Each unit increase in model scale brings progressively smaller performance gains."

**Statistical Results:**
Quadratic OLS, test H1: β2<0 (AnesBench/AMCQA × System):
- AnesBench: β2=-2.79; p=0.006 (System1); β2=-2.607; p=0.007 (System1.x); β2=-1.920; p=0.008 (System2)
- AMCQA: β2=-2.923; p=0.010 (System1); β2=-2.729; p=0.017 (System1.x); β2=-2.587; p=0.023 (System2)

**Key Findings:**
- Consistent with our claim.

**Claim 4.1c (L218)**: "This indicates that performance gains from increasing model size are notably lower for System2 compared to System1."

**Statistical Results:**
Linear model: y=β0+β1x+β2·group+β3(x×group)+ε, where group indicates x from System1 (or System1.x). Bootstrap one-sided p for H1:β3>0:
- 0.1538 (System1>System2); 0.0009 (System1.x>System2)

**Key Findings:**
- This claim holds only for System 1.x.

### 4.2 Performance Across Language

**Claim 4.2a (L232)**: "In contrast, Llama-3.1-8B-based models, ...., perform notably worse in Chinese."

**Statistical Results:**
Bootstrap comparisons (English>Chinese):
- Llama-3.1-8B-Instruct: AnesBench p=9e-5; AMCQA p=9e-5
- UltraMedical-8B: AnesBench p=9e-5; AMCQA p=9e-5
- Huatuo-GPT-8B: AnesBench p=9e-5; AMCQA p=0.0059
- FineMedLM: AnesBench p=9e-5; AMCQA p=9e-5
- FineMedLM-o1: AnesBench p=9e-5; AMCQA p=9e-5

**Key Findings:**
- Consistent with our claim.

### 4.3 Performance Across Output Length

**Claim 4.3a (L248)**: "Models with longer CoT reasoning processes tend to exhibit superior response performance... for system2,... models achieve higher scores with longer outputs."

**Statistical Results:**
Spearman within each system (output log length vs. score):
- System1: rho=0.2879; p=0.3182
- System1.x: rho=0.3319; p=0.2464
- System2: rho=0.4917; p=0.0741

**Key Findings:**
- Consistent with our claim, though with weak evidence (0.05<p<0.1).

---

## Response to Data Quality Concerns

Thank you for carefully reviewing our benchmark and datasets.

### AnesQA Quality Assessment

Regarding the use of LLMs, we mentioned in line 159 of the main text that AnesQA is generated and filtered using LLMs. We apologize for the oversight in omitting this case in Checklist Item 16.

The incorrect QA examples were caused by the LLM mistakenly outputting the prompt under a one-shot setting. However, such errors are rare (~0.5%) and have minimal impact on the overall dataset quality.

### AnesBench Quality Assessment

As noted in Appendix J, our initial quality check involved random sampling of 500 examples, during which no such issues were detected. We found that the issue you mentioned stems from a single data source error. After thoroughly reviewing **all** data from AnesBench, we identified erroneous question IDs as follows (9/4427 ≈ 0.2%; Also rare, minimal impact on the overall benchmark quality):

**Error:**
- 33c58654-0add-5ed7-a1f6-4d788b419036
- 33f6f51b-d715-5c65-945d-34f6aa4362c0
- 6f32034f-02d5-58d1-84c5-8cf0533fb412
- 3b6200f0-1747-5b82-92cd-67ea1148357e
- 5aebf7c7-a549-59a1-b3ae-e3f3e8cdd886

**Missing required information:**
- ee133db3-c6e2-53b1-b601-846c1680ba63
- d50c6e15-a4d5-5812-94ea-5d81ce8dee71
- 4a4a0859-1e82-5614-b0e8-97b9a8c50beb
- 98f7b24d-252d-5f6e-9b12-e6289489a62b

We will upload the corrected version to HuggingFace and publish an errata list on both GitHub and the project website to ensure ongoing maintenance of our benchmark and dataset.

---

## Response to Limitation Concerns

Thanks for your comment. We believe it is appropriate to include the limitations in the main text. In the revised manuscript, we will report the limitations—such as the limited variety of question types and the noise introduced by the LLM—within the main text for readers' reference.

---

## Conclusion

We believe these detailed responses comprehensively address the reviewer's concerns and provide the statistical rigor requested. The extensive statistical validation demonstrates the robustness of our claims while revealing nuanced insights about the effectiveness of different training strategies across various reasoning complexity levels. We will incorporate all these statistical analyses and clarifications into the revised manuscript to ensure complete transparency regarding our methodology and findings.

---

## References

[1] EvoLM: In Search of Lost Language Model Training Dynamics
