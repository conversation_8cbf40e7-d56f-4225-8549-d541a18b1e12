## Response to Reviewer 6vid

We sincerely thank you for the careful and thoughtful comments. Below we address the key concerns.

1.   <PERSON>k of systematic analysis on question length
(Q: No systematic reporting of question length distributions across the three reasoning levels)

A: Thank you very much for your valuable comments. As reported in Figure 1 of our paper, we have already provided basic statistics regarding question length, including the average length of the question stem (measured in tokens) and the average number of options. We also agree with your suggestion that analyzing question length across the three different levels of reasoning complexity is important. However, since we are unable to upload additional files during the rebuttal phase, we have presented the length distribution information in tabular form instead. We will incorporate a more detailed information (e.g. figures) in the revised version of the paper.

### System1
| Bin Range | Frequency | Proportion |
|:------------|------------:|-------------:|
| 0-30 | 2562 | 0.852579 |
| 30-60 | 354 | 0.117804 |
| 60-90 | 67 | 0.0222962 |
| 90-120 | 10 | 0.00332779 |
| 120-150 | 7 | 0.00232945 |
| 150-180 | 1 | 0.000332779 |
| 180-210 | 3 | 0.000998336 |
| 210-240 | 1 | 0.000332779 |

### System1.x
| Bin Range | Frequency | Proportion |
|:------------|------------:|-------------:|
| 0-30 | 297 | 0.280189 |
| 30-60 | 453 | 0.427358 |
| 60-90 | 197 | 0.185849 |
| 90-120 | 80 | 0.0754717 
| 120-150 | 24 | 0.0226415 |
| 150-180 | 6 | 0.00566038 |
| 180-210 | 3 | 0.00283019 |
| 210-240 | 0 | 0 |

### System2
| Bin Range | Frequency | Proportion |
|:------------|------------:|-------------:|
| 0-30 | 50 | 0.138122 |
| 30-60 | 100 | 0.276243 |
| 60-90 | 110 | 0.303867 |
| 90-120 | 69 | 0.190608 |
| 120-150 | 22 | 0.0607735 |
| 150-180 | 8 | 0.0220994 |
| 180-210 | 3 | 0.00828729 |
| 210-240 | 0 | 0 

### Overall
| Bin Range | Frequency | Proportion |
|:------------|------------:|-------------:|
| 0-30 | 2909 | 0.657104 |
| 30-60 | 907 | 0.204879 |
| 60-90 | 374 | 0.0844816 |
| 90-120 | 159 | 0.035916 |
| 120-150 | 53 | 0.011972 |
| 150-180 | 15 | 0.0033883 |
| 180-210 | 9 | 0.00203298 |
| 210-240 | 1 | 0.000225887 |

From the table, we observe that the question lengths across all three levels, as well as overall, exhibit a clearly positively skewed distribution. Moreover, the average question length increases progressively with the level of reasoning complexity. The most frequent length intervals for System 1, System 1.x, and System 2 questions are 0–30, 30–60, and 60–90 tokens, respectively. This trend not only highlights the differences in reasoning difficulty among the levels but also reflects how such differences manifest in the length of the questions.




2. Missing token count statistics for questions and answers


Thank you for your comments. Following your recommendation, we computed token counts for the benchmark’s questions and answers. The table below reports the token‐count statistics for questions and answers across the different reasoning levels, including the mean, median, and variance. Consistent with above pattern, the token counts of both questions and answers are positively associated with the reasoning level; higher levels correspond to longer questions as well as longer answers.

| Level | Question Mean | Question Median | Question Variance | Aneswer Mean | Answer Median | Answer Variance |
|:----------|----------------:|------------------:|--------------------:|--------------:|----------------:|------------------:|
| System1 | 20.582 | 17 | 244.757 | 6.82772 | 5 | 32.234 |
| System1.x | 49.9132 | 44 | 880.109 | 7.66702 | 6 | 40.7965 |
| System2 | 70.9641 | 69.5 | 1282.23 | 8.93498 | 7 | 43.7658 |
| Overall | 31.7249 | 21 | 771.116 | 7.21792 | 5 | 35.7545 |

We additionally present, across the different levels, the length distributions for all answers as well as for the subset of correct answers. The results exhibit a clear trend: correct answers tend to be, on average, longer than the overall set of answers.

| Level | P20| P40| P60 | P80 | P100 |
|:----------|:------|:------|:------|:------|:--------|
| System1 | 3/3 | 4/5 | 6/8 | 10/14 | 146/250 |
| System1.x | 3/3 | 5/5 | 7/9 | 11/15 | 105/210 |
| System2 | 4/4 | 6/7 | 8/11 | 13/19 | 63/297 |
| Overall | 3/3 | 4/5 | 7/8 | 11/15 | 146/297 |


3. Absence of computational complexity analysis for different reasoning types

Thank you very much for your suggestion. We attempted to assess the differences in computational complexity across difficulty levels using the following approach: since it is challenging to directly compute the absolute computational complexity of a problem, we modeled complexity using a token-level metric. Specifically, we hypothesize that the computational complexity of a question is proportional to the total number of output tokens required to answer it correctly, i.e., $O(L_{\text{out}})$.

To this end, we selected the top 20 models with the highest average scores on AnesBench and AMCQA. For each question, we computed the average output length (in tokens) across these models when the answer was correct. Based on this, we report the following statistics: mean, median, variance, the 20/40/60/80/100 percentiles, and the proportion of questions at each level for which all 20 models failed to produce a correct answer.

| Level | Mean | Median | Variance | Coefficient of Variation | P20 | P40 | P60 | P80 | P100 | All Wrong Ratio |
|:--------|--------:|---------:| ------: |-----------:|--------:|--------:|--------:|---------:|-------:|------------------:|
| System1 | 601.561 | 556.623 | 45876.1 | 0.356  |438.002 | 513.579 | 608.626 | 739.204 | 2019 | 0.0236273 |
| System1.x| 777.862 | 741.35 | 65728.4 | 0.330|574.13 | 691.088 | 799.81 | 951.026 | 2025 | 0.0292453 |
| System2 | 871.518 | 826.2 | 77065.5 | 0.319|681.462 | 780.656 | 877.783 | 1034.75 | 2022 | 0.0524862 |
| Overall | 665.195 | 620.446 | 62288 | 0.375|463.1 | 562.625 | 681.263 | 834.9 | 2025 | 0.0273323 |


Based on the above statistical results, we observe that, overall, as the level increases from System1 to System1.x and then to System2, the proxy metric for computational complexity—measured by the average output length of correct answers—shows a consistent increase in both mean and median. This growth is not driven by a small number of outlier cases with excessively long answers, but rather by a general upward shift across all quantiles, indicating that the shortest correct solutions for most problems tend to be longer. Additionally, while the variance increases, the coefficient of variation slightly decreases, suggesting that the variability becomes relatively more concentrated around a higher mean. Moreover, the rate of entirely incorrect answers (“All Wrong Ratio”) rises significantly with increasing problem level, reflecting that higher reasoning levels not only demand more computation but are also more likely to exceed the current capabilities of the model.































